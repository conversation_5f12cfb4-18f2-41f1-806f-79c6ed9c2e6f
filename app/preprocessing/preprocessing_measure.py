import time
from contextai.Schema import Document
from typing import List, Optional, Literal, TYPE_CHECKING
import pandas as pd
import json
from contextdb.Engine.sqlite_database import SQLiteEngine
from app.utils.logging import setup_logging

# Import SQL queries
from .queries.reference_queries import *
from .queries.attribute_queries import *
from .queries.measure_queries import *
from .queries.product_mapping_queries import *
from .queries.inference_queries import *

if TYPE_CHECKING:
    from contextai.Embeddings.base_embeddings import BaseEmbeddings
    from contextai.VectorDatabase.base_vectordb import BaseVectorDatabase
    from contextdb.Engine.base_database import BaseDatabaseEngine

CAT_ATTRIBUTE_TABLE = "data_manager.attribute_group_attribute_field_all_cat_full_view"


# Create a global logger instance
logger = setup_logging()


class Preprocessor:

    def __init__(
        self,
        database: "BaseDatabaseEngine",
        embeddings: "BaseEmbeddings",
        base_dir: str,
        vdb_type: Literal["faiss", "postgres"] = "faiss",
        skip_id_list: Optional[List[int]] = None,
    ) -> None:
        # Setup instance-specific logger with context
        self.logger = logger.bind(database=getattr(database, "database", "unknown"))

        self.logger.info(
            "Initializing preprocessor",
            skip_ids_count=len(skip_id_list) if skip_id_list else 0,
        )

        self._base_dir = base_dir
        self._database = database
        self._embeddings = embeddings
        self._vdb_type = vdb_type
        self._skip_id_list = skip_id_list or []

    @property
    def base_dir(self) -> str:
        return self._base_dir

    @property
    def database(self) -> "BaseDatabaseEngine":
        return self._database

    @property
    def embeddings(self) -> "BaseEmbeddings":
        return self._embeddings

    @property
    def preprocessing_dir(self) -> str:
        import os

        return os.path.join(self.base_dir, self.database.database)

    @property
    def vdb_dir(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "faiss_vdb")

    @property
    def score_matrix_dir(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "score_matrix")

    @property
    def attribute_ids_size_json(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "attribute_ids_size.json")

    @property
    def attribute_ids_by_pcat_json(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "attribute_ids_by_pcat.json")

    @property
    def pcat_id_size_json(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "pcat_id_size.json")

    @property
    def score_matrix_size_json(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "score_matrix_size.json")

    @property
    def product_mapping_dir(self) -> str:
        import os

        return os.path.join(self.preprocessing_dir, "product_mapping")

    @property
    def vdb(self) -> "BaseVectorDatabase":
        if not hasattr(self, "_vdb"):
            if self._vdb_type == "faiss":
                from contextai.VectorDatabase.faiss_vectordb import FaissVectorDatabase

                self._vdb = FaissVectorDatabase(
                    embeddings=self.embeddings, split_text=False, vdb_dir=self.vdb_dir
                )
            else:
                # For PGVector, needs to be refactored
                pass
        return self._vdb

    @property
    def skip_ids(self):
        return self._skip_id_list

    @property
    def sqlite_path(self):
        """Path to the SQLite database directory.

        Returns:
            str: Path to the SQLite database directory.
        """
        import os

        path = os.path.join(self.preprocessing_dir, "sqlite")
        os.makedirs(path, exist_ok=True)
        return path

    @property
    def local_sqlite_db(self):
        """SQLiteEngine instance for mtbase data.

        Returns:
            SQLiteEngine: Instance of SQLiteEngine for mtbase data.
        """
        if not hasattr(self, "_local_sqlite_db"):
            import os

            # Ensure the preprocessing directory exists before creating the database
            os.makedirs(self.preprocessing_dir, exist_ok=True)

            self._local_sqlite_db = SQLiteEngine(
                f"{self.preprocessing_dir}/local_sqlite_db.sqlite"
            )
        return self._local_sqlite_db

    @property
    def attributes_by_category_table(self) -> str:
        """Table name of attribute order in their respective category.

        Returns:
            str: Table name of attribute order in their respective category.
        """
        table_name = "attribute_order"
        if not hasattr(self, "_attributes_by_category_table"):
            # Fix the empty NOT IN clause issue
            if self.skip_ids:
                ids_sql = ", ".join(map(str, self.skip_ids))
                where_clause = f"WHERE attribute_id NOT IN ({ids_sql})"
            else:
                where_clause = ""

            self._attributes_by_category_table = self.local_sqlite_db.df_to_table(
                self.local_sqlite_db.query(
                    ATTRIBUTES_BY_CATEGORY_QUERY.format(where_clause=where_clause)
                ),
                table_name=table_name,
                if_exists="replace",
            )
        return self._attributes_by_category_table

    def _create_mtbase(self):
        self.logger.info("Fetching all measure data for analysis")
        mtbase_all_df = self.local_sqlite_db.query(MTBASE_CREATION_QUERY)

        self.logger.info("Storing measure data in mtbase database for analysis")
        self.local_sqlite_db.df_to_table(
            mtbase_all_df, table_name="mtbase_all", if_exists="replace"
        )

    def _create_combined_attribute_counts(self):
        count_measure = self.local_sqlite_db.query(MEASURE_ANALYSIS_QUERY)
        count_term = self.local_sqlite_db.query(TERM_COUNTS_ANALYSIS_QUERY)
        self.attribute_ids_size_measure = dict(
            zip(count_measure["attribute_id"], count_measure["term_count"])
        )
        self.attribute_ids_size_term = {
            **dict(zip(count_term["attribute_id"], count_term["term_count"]))
        }
        combined_count = pd.concat([count_measure, count_term], ignore_index=True)
        self.local_sqlite_db.df_to_table(
            combined_count, table_name="combined_count", if_exists="replace"
        )

    def _save_reference_tables_locally(self):
        # save attribute group attribute locally
        self.logger.info("Saving attribute group attribute locally")
        self.local_sqlite_db.df_to_table(
            self.database.query(
                ATTRIBUTE_GROUP_ATTRIBUTE_QUERY.format(
                    cat_attribute_table=CAT_ATTRIBUTE_TABLE
                )
            ),
            table_name="attribute_group_attribute",
            if_exists="replace",
        )
        pcat_id = self.local_sqlite_db.query(DISTINCT_PCAT_IDS_QUERY)[
            "product_category_id"
        ].to_list()

        # save product locally
        self.logger.info("Saving product locally")
        self.local_sqlite_db.df_to_table(
            self.database.query(
                PRODUCTS_BY_CATEGORIES_QUERY.format(
                    pcat_ids=", ".join(map(str, pcat_id))
                )
            ),
            table_name="product",
            if_exists="replace",
        )

        # save product attribute locally
        attribute_id = self.local_sqlite_db.query(DISTINCT_ATTRIBUTE_IDS_QUERY)[
            "attribute_id"
        ].to_list()
        self.logger.info("Saving product attribute locally")
        self.local_sqlite_db.df_to_table(
            self.database.query(
                PRODUCT_ATTRIBUTES_BY_CATEGORIES_QUERY.format(
                    attribute_ids=", ".join(map(str, attribute_id))
                )
            ),
            table_name="product_attribute",
            if_exists="replace",
        )

        # save attribute locally
        self.logger.info("Saving attribute locally")
        self.local_sqlite_db.df_to_table(
            self.database.query(
                ATTRIBUTES_BY_IDS_QUERY.format(
                    attribute_ids=", ".join(map(str, attribute_id))
                )
            ),
            table_name="attribute",
            if_exists="replace",
        )

        # save attribute_term locally
        self.logger.info("Saving attribute_term locally")
        self.local_sqlite_db.df_to_table(
            self.database.query(ATTRIBUTE_TERMS_QUERY),
            table_name="attribute_term",
            if_exists="replace",
        )

        # save measure unit locally
        self.logger.info("Saving measure unit locally")
        self.local_sqlite_db.df_to_table(
            self.database.query(MEASURE_UNITS_QUERY),
            table_name="measure_unit",
            if_exists="replace",
        )

        # save mtbase data locally
        self.logger.info("Saving mtbase data locally")
        self._create_mtbase()

        # save combined count locally
        self.logger.info("Saving combined count locally")
        self._create_combined_attribute_counts()

    def _attribute_ids_by_pcat(self) -> None:
        ao = self.local_sqlite_db.query(
            ATTRIBUTE_IDS_BY_PCAT_QUERY.format(
                attributes_by_category_table=self.attributes_by_category_table
            )
        )[["product_category_id", "attribute_id"]]
        attribute_ids_by_pcat = (
            ao.groupby("product_category_id")["attribute_id"]
            .apply(lambda x: list(x))
            .to_dict()
        )

        with open(self.attribute_ids_by_pcat_json, "w") as f:
            json.dump(attribute_ids_by_pcat, f)

    def _calculate_product_category_id_size(self) -> None:
        nof_products_id = {}
        pcat_ids = (
            self.local_sqlite_db.query(DISTINCT_PCAT_IDS_FROM_PRODUCT_QUERY)[
                "product_category_id"
            ]
            .dropna()
            .to_list()
        )
        for id in pcat_ids:
            count = self.local_sqlite_db.query(
                COUNT_PRODUCTS_BY_PCAT_QUERY.format(pcat_id=id)
            )["count"].to_list()[0]
            nof_products_id[int(id)] = count
        with open(self.pcat_id_size_json, "w") as f:
            json.dump(nof_products_id, f)

    def _create_vectordb(self) -> None:
        """Creating the vector database for attribute terms."""
        start_time = time.time()
        self.logger.info("Creating vector database for attribute terms")

        term_df = self.local_sqlite_db.query(
            ATTRIBUTE_TERMS_FOR_VDB_QUERY.format(
                attributes_by_category_table=self.attributes_by_category_table
            )
        )

        terms = term_df["term"].values
        metadatas = term_df[["attribute_id", "term_order", "type"]].to_dict("records")
        docs = [Document(text=x[0], metadata=x[1]) for x in zip(terms, metadatas)]

        self.logger.info(
            "Inserting attribute terms into vector database", term_count=len(terms)
        )

        self.vdb.add_documents(docs=docs, split_text=False)

        end_time = time.time() - start_time
        self.logger.info(
            "Vector database creation completed",
            time_taken_seconds=round(end_time, 2),
            document_count=len(docs),
        )

    def _create_score_matrices(self):
        from .matrix_operations.score_matrix_creation import create_score_matrices

        create_score_matrices(
            score_matrix_dir=self.score_matrix_dir,
            attribute_ids_size_term=self.attribute_ids_size_term,
            attribute_ids_size_measure=self.attribute_ids_size_measure,
            vdb=self.vdb,
            local_sqlite_db=self.local_sqlite_db,
            logger=self.logger,
        )

    def _compute_product_mapping(self):
        """Create product mappings for all product categories."""
        from .matrix_operations.product_mapping import compute_product_mapping_parallel

        # Get the database path from the SQLiteEngine
        db_path = f"{self.preprocessing_dir}/local_sqlite_db.sqlite"

        compute_product_mapping_parallel(
            product_mapping_dir=self.product_mapping_dir,
            local_sqlite_db=self.local_sqlite_db,
            attributes_by_category_table=self.attributes_by_category_table,
            logger=self.logger,
            db_path=db_path,
        )

    def _copy_tables_for_inference(self):
        """Create JSON metadata files needed for inference system.

        This method generates three key files:
        - pcat_id_size.json: Product counts by category
        - attribute_ids_by_pcat.json: Attribute IDs organized by product category
        - attribute_ids_size.json: Attribute term counts for sizing
        """
        self.logger.info("Creating inference metadata files")

        try:
            # 1. Generate product category sizes
            self._create_pcat_id_size_for_inference()
            self.logger.info("Product category sizes calculated for inference")

            # 2. Generate attribute IDs by product category
            self._create_attribute_ids_by_pcat_for_inference()
            self.logger.info("Attribute IDs by category calculated for inference")

            # 3. Generate attribute sizes for inference
            self._create_attribute_sizes_for_inference()
            self.logger.info("Attribute sizes calculated for inference")

            self.logger.info("All inference metadata files created successfully")

        except Exception as e:
            self.logger.error(f"Failed to create inference metadata: {str(e)}")
            raise

    def _create_pcat_id_size_for_inference(self):
        """Create pcat_id_size.json from local SQLite data for inference."""
        nof_products_id = {}
        pcat_ids = (
            self.local_sqlite_db.query(DISTINCT_PCAT_IDS_FOR_INFERENCE_QUERY)[
                "product_category_id"
            ]
            .dropna()
            .to_list()
        )

        for pcat_id in pcat_ids:
            count = self.local_sqlite_db.query(
                COUNT_PRODUCTS_BY_PCAT_FOR_INFERENCE_QUERY.format(pcat_id=pcat_id)
            )["count"].to_list()[0]
            nof_products_id[int(pcat_id)] = count

        with open(self.pcat_id_size_json, "w") as f:
            json.dump(nof_products_id, f)

    def _create_attribute_ids_by_pcat_for_inference(self):
        """Create attribute_ids_by_pcat.json from local SQLite data for inference."""
        ao = self.local_sqlite_db.query(
            ATTRIBUTE_IDS_BY_PCAT_FOR_INFERENCE_QUERY.format(
                attributes_by_category_table=self.attributes_by_category_table
            )
        )[["product_category_id", "attribute_id"]]

        attribute_ids_by_pcat = (
            ao.groupby("product_category_id")["attribute_id"]
            .apply(lambda x: list(x))
            .to_dict()
        )

        with open(self.attribute_ids_by_pcat_json, "w") as f:
            json.dump(attribute_ids_by_pcat, f)

    def _create_attribute_sizes_for_inference(self):
        """Create attribute_ids_size.json from local SQLite data for inference."""
        df = self.local_sqlite_db.query(ATTRIBUTE_SIZES_FOR_INFERENCE_QUERY)
        attribute_ids_size = dict(zip(df["attribute_id"], df["term_count"]))

        with open(self.attribute_ids_size_json, "w") as f:
            json.dump(attribute_ids_size, f)

    def _copy_tables_for_validation(self):
        """For the validation we need to copy the product_category_id"""
        pass

    def run_preprocessing(self):
        """Run the complete preprocessing pipeline."""
        start_time = time.time()
        self.logger.info("Starting preprocessing pipeline")

        try:
            self._save_reference_tables_locally()
            self._create_vectordb()
            self.logger.info("Vector database created")

            self._create_score_matrices()
            self.logger.info("Score matrices created")

            self._compute_product_mapping()
            self.logger.info("Product mappings computed")

            self._copy_tables_for_inference()
            self.logger.info("Inference metadata files created")

            total_time = time.time() - start_time
            self.logger.info(
                "Preprocessing completed successfully",
                total_time_seconds=round(total_time, 2),
            )
        except Exception as e:
            self.logger.error(
                "Preprocessing failed",
                error=str(e),
                total_time_seconds=round(time.time() - start_time, 2),
                exc_info=True,
            )
            raise
